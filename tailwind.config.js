/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // Custom colors that complement MUI theme
      colors: {
        primary: {
          50: '#e3f2fd',
          100: '#bbdefb',
          200: '#90caf9',
          300: '#64b5f6',
          400: '#42a5f5',
          500: '#2196f3',
          600: '#1e88e5',
          700: '#1976d2',
          800: '#1565c0',
          900: '#0d47a1',
        },
        secondary: {
          50: '#fce4ec',
          100: '#f8bbd9',
          200: '#f48fb1',
          300: '#f06292',
          400: '#ec407a',
          500: '#e91e63',
          600: '#d81b60',
          700: '#c2185b',
          800: '#ad1457',
          900: '#880e4f',
        },
      },
      // Custom spacing that matches MUI's 8px grid
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      // Custom border radius to match MUI theme
      borderRadius: {
        'mui': '8px',
        'mui-lg': '12px',
      },
      // Custom font family to match MUI
      fontFamily: {
        'sans': ['Roboto', '-apple-system', 'BlinkMacSystemFont', '"Segoe UI"', '"Helvetica Neue"', 'Arial', 'sans-serif'],
      },
      // Custom shadows to complement MUI
      boxShadow: {
        'mui': '0 2px 8px rgba(0,0,0,0.1)',
        'mui-lg': '0 4px 16px rgba(0,0,0,0.15)',
      },
    },
  },
  plugins: [],
  // Important: Use important to ensure Tailwind classes override MUI when needed
  important: true,
  // Prefix to avoid conflicts with MUI classes
  // prefix: 'tw-',
};
