{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/api/client.ts", "../../src/api/index.ts", "../../src/api/types.ts", "../../src/api/services/postservice.ts", "../../src/api/services/userservice.ts", "../../src/components/footer.tsx", "../../src/components/layout.tsx", "../../src/components/navbar.tsx", "../../src/components/userslist.tsx", "../../src/components/index.ts", "../../src/components/common/errorboundary.tsx", "../../src/components/common/loadingspinner.tsx", "../../src/components/common/index.ts", "../../src/config/env.ts", "../../src/constants/index.ts", "../../src/hooks/index.ts", "../../src/hooks/useposts.ts", "../../src/hooks/useusers.ts", "../../src/pages/about.tsx", "../../src/pages/home.tsx", "../../src/pages/notfound.tsx", "../../src/pages/index.ts", "../../src/query/queryprovider.tsx", "../../src/query/queryclient.ts", "../../src/router/index.tsx", "../../src/theme/themeprovider.tsx", "../../src/theme/index.ts", "../../src/types/index.ts", "../../src/utils/index.ts"], "errors": true, "version": "5.8.3"}