/* Import Roboto font */
@import '@fontsource/roboto/300.css';
@import '@fontsource/roboto/400.css';
@import '@fontsource/roboto/500.css';
@import '@fontsource/roboto/700.css';

/* Import Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles that work with both MUI and Tailwind */
@layer base {
  html {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  }

  body {
    margin: 0;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Custom component styles */
@layer components {
  .app-container {
    @apply min-h-screen bg-gray-50;
  }

  .card-shadow {
    @apply shadow-mui;
  }

  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-mui transition-colors;
  }

  .btn-secondary {
    @apply bg-secondary-600 hover:bg-secondary-700 text-white font-medium py-2 px-4 rounded-mui transition-colors;
  }
}

/* Utility classes for MUI integration */
@layer utilities {
  .mui-spacing {
    @apply space-y-2;
  }

  .text-mui-primary {
    color: #1976d2;
  }

  .text-mui-secondary {
    color: #dc004e;
  }
}
